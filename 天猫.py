import time
import random
from device_utils import get_device, get_device_name
from ui_automation_utils import find_and_click



def random_swipe(duration_sec=40, interval_sec=2, x_radius=30, 
                y_radius=300, min_swipe_distance=100):
    """
    每隔指定时间在屏幕中心附近随机滑动，持续指定时间

    :param duration_sec: 总持续时间(秒)，默认40秒
    :param interval_sec: 滑动间隔时间(秒)，默认2秒
    :param radius: 中心点附近的像素范围
    :param min_swipe_distance: 最小滑动距离（Y 轴），默认100
    """
    d = get_device()
    # 获取设备屏幕尺寸
    screen_info = d.info
    screen_width = screen_info['displayWidth']
    screen_height = screen_info['displayHeight']
    width, height = screen_width, screen_height
    # 计算中心点坐标
    center_x = width // 2
    center_y = height // 2
    # 计算需要执行的滑动次数
    iterations = int(duration_sec / interval_sec)

    for i in range(iterations):
        # 生成 X 轴随机坐标（限制在中心附近）
        start_x = random.randint(center_x - x_radius, center_x + x_radius)
        end_x = random.randint(center_x - x_radius, center_x + x_radius)

        # 生成 Y 轴随机坐标，并确保滑动距离 ≥ min_swipe_distance
        while True:
            start_y = random.randint(center_y - y_radius, center_y + y_radius)
            end_y = random.randint(center_y - y_radius, center_y + y_radius)
            if abs(start_y - end_y) >= min_swipe_distance:
                break  # 确保滑动距离足够

        # 确保坐标不超出屏幕范围
        start_x = max(0, min(start_x, width - 1))
        start_y = max(0, min(start_y, height - 1))
        end_x = max(0, min(end_x, width - 1))
        end_y = max(0, min(end_y, height - 1))

        # 执行滑动操作
        d.swipe(start_x, start_y, end_x, end_y)

        # 打印当前进度
        print(f"已完成 {i+1}/{iterations} 次滑动，等待下一次...")

        # 如果不是最后一次，则等待间隔时间
        if i < iterations - 1:
            time.sleep(interval_sec)

        # 判断是否完成提前结束
        if find_and_click("返回领奖", click=False, screenshot=False) is not None or find_and_click("任务已完成快去领奖吧", click=False, screenshot=False) is not None:
            print("提示返回了，操作完成")
            break
    print("随机滑动操作完成")

"""执行福气任务"""
def 福气任务():

    # 方法实现开始...
    d = get_device()

    find_and_click("我")
    find_and_click("福气红包",wait_after_click=5)

    #在任务界面开始。。

    find_and_click("昨日任务")
    find_and_click("昨日任务")

    if find_and_click("福福福发福呦") is not None:
        print("进入了福福福发福呦")
        if find_and_click("兑红包红包天天上新",click=False)  is None:
            print("没找到兑红包红包天天上新,返回一下")
            d.press("back")
            time.sleep(2)
            find_and_click("福福福发福呦")


    find_and_click("今日签到")

    #任务交叉
    find_and_click("O1CN01eVRrEP1UBQnatGxTV_!!6000000002479-2-tps-96-96.png_")

    # 攒福气 
    if find_and_click('(//*[contains(@resource-id, "task-bubble")])[last()]/following-sibling::*[3]', match_type='xpath') is not None:

        # 开始循环做任务
        while True:
            press_back_time = 2

            # XPath表达式
            xpath = '//*[ contains(@text,"领200福气") \
            or @text="点商品，可领100福气" \
            or @text="点商品，可领50福气"  \
            or @text="搜索浏览商品，领50福气" \
            or @text="浏览30s,最高得50福气" \
            ]'

            if find_and_click(xpath, match_type='xpath', click=False) is not None:
                # 下一个节点是任务按钮
                if find_and_click(xpath+'/following-sibling::*', match_type='xpath') is not None:
                    # 还在当前界面说明是领取，继续点做任务
                    if find_and_click("今天做任务 明天领福气") is not None:
                        continue

                    # 进入任务界面
                    if find_and_click("搜索", click=False) is not None:
                        ele = find_and_click(
                            '//*[@text="搜索"]/..//android.widget.EditText', match_type='xpath', click=False)
                        if ele:
                            #历史搜索 
                            find_and_click(
                            '//*[@text="历史搜索"]/following-sibling::*//android.view.View', match_type='xpath')
                            #猜你想搜
                            find_and_click(
                            '//*[@text="猜你想搜"]/following-sibling::*[2]//android.view.View', match_type='xpath')
                        else:
                            print("未找到搜索输入框")

                    else:
                        print("没有搜索")

                    random_swipe()
                    # 返回,最多press_back_time 2次，到了任务也就停
                    for i in range(press_back_time):
                        if not find_and_click("今天做任务 明天领福气"):
                            d.press("back")
                            time.sleep(1)
            else:
                print("没有找到任务")
                break

        find_and_click("领取")

def 抢红包_job(d):
    
    # 回到主页
    d.press("home")
    # 等待主页加载
    time.sleep(1)


    find_and_click("我")
    find_and_click("福气红包",wait_after_click=5)



    find_and_click("兑红包红包天天上新",wait_after_click=5)

    find_and_click('//*[@text="通用红包"]/following-sibling::*[@text="2000可兑"]/following-sibling::*[@text="点击兑换"]',match_type='xpath')

    find_and_click('(//*[@text="确认兑换"])[2]',match_type='xpath')

    if find_and_click('//*[contains(@text,"今日已兑换")]',match_type='xpath'):
        print('今日已兑换')
        return True
    else:
        print('今日未兑换,继续尝试操作')
        return False
    
def 抢红包():
    # 方法实现开始...
    d = get_device()
    
    while True:

        if 抢红包_job(True) :
            break
        else:
            d.press('back')
            time.sleep(1)
            d.press('back')
            time.sleep(1)
            d.press('back')
            time.sleep(1)
            continue    
    



