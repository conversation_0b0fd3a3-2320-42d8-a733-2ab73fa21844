import time
import random
from device_utils import get_device, get_device_name
from ui_automation_utils import find_and_click



def random_swipe(duration_sec=40, interval_sec=2, x_radius=30, 
                y_radius=300, min_swipe_distance=100):
    """
    每隔指定时间在屏幕中心附近随机滑动，持续指定时间

    :param duration_sec: 总持续时间(秒)，默认40秒
    :param interval_sec: 滑动间隔时间(秒)，默认2秒
    :param radius: 中心点附近的像素范围
    :param min_swipe_distance: 最小滑动距离（Y 轴），默认100
    """
    d = get_device()
    # 获取设备屏幕尺寸
    screen_info = d.info
    screen_width = screen_info['displayWidth']
    screen_height = screen_info['displayHeight']
    width, height = screen_width, screen_height
    # 计算中心点坐标
    center_x = width // 2
    center_y = height // 2
    # 计算需要执行的滑动次数
    iterations = int(duration_sec / interval_sec)

    for i in range(iterations):
        # 生成 X 轴随机坐标（限制在中心附近）
        start_x = random.randint(center_x - x_radius, center_x + x_radius)
        end_x = random.randint(center_x - x_radius, center_x + x_radius)

        # 生成 Y 轴随机坐标，并确保滑动距离 ≥ min_swipe_distance
        while True:
            start_y = random.randint(center_y - y_radius, center_y + y_radius)
            end_y = random.randint(center_y - y_radius, center_y + y_radius)
            if abs(start_y - end_y) >= min_swipe_distance:
                break  # 确保滑动距离足够

        # 确保坐标不超出屏幕范围
        start_x = max(0, min(start_x, width - 1))
        start_y = max(0, min(start_y, height - 1))
        end_x = max(0, min(end_x, width - 1))
        end_y = max(0, min(end_y, height - 1))

        # 执行滑动操作
        d.swipe(start_x, start_y, end_x, end_y)

        # 打印当前进度
        print(f"已完成 {i+1}/{iterations} 次滑动，等待下一次...")

        # 如果不是最后一次，则等待间隔时间
        if i < iterations - 1:
            time.sleep(interval_sec)

        # 判断是否完成提前结束
        if find_and_click("返回领奖", click=False, screenshot=False) is not None or find_and_click("任务已完成快去领奖吧", click=False, screenshot=False) is not None:
            print("提示返回了，操作完成")
            break
    print("随机滑动操作完成")




def 视频任务():
    
    d = get_device()
    
    # run_swipe_loop2(d)
    # find_and_click("领取奖励")
    # find_and_click("去赚钱")

    # find_and_click("//*[@text='看6次直播领金币']/../..//*[@text='领福利']",match_type='xpath')

    # find_and_click("//*[@resource-id='com.kuaishou.nebula:id/play_view_container']",match_type='xpath')
    #代表有金币计时
    #//*[@resource-id="com.kuaishou.nebula:id/neo_count_down_image_group"] 
    #等一分钟，返回


    ##################################

    # find_and_click("//*[@text='看指定视频赚金币']/../..//*[@text='去观看']",match_type='xpath')

    #循环滑动

    ##############################

    # find_and_click("//*[@text='刷广告视频赚金币']/../..//*[@text='领福利']",match_type='xpath')

    # #循环滑动
    # run_swipe_loop2(d)

    #########################
    # 看广告得金币

    while True:
        # 任务开始
        if find_and_click("//*[@text='看广告得金币']/../..//*[@text='领福利']", match_type='xpath') is not None:
            print("找到并点击了'领福利'按钮")
            
            # 每2秒判断一次是否成功领取
            while True:
                time.sleep(2)
                
                # 检查是否已成功领取
                if find_and_click("//*[contains(@text,'已成功领取')]", match_type='xpath', click=False) is not None:
                    print("检测到'已成功领取'，返回")
                    d.press('back')
                    time.sleep(2)
                    
                    # 处理返回后的三种情况
                    # 情况1：直接返回到任务页（继续外层循环）
                    if find_and_click("//*[@text='看广告得金币']/../..//*[@text='领福利']", match_type='xpath', click=False):
                        print("已返回任务页")
                        break  # 跳出内层循环，继续外层循环
                    
                    # 情况2：弹窗 - 领取奖励
                    elif find_and_click("领取奖励"):
                        print("点击了'领取奖励'，继续观看下一个视频")
                        time.sleep(3)
                        # 继续内层循环（继续检查是否已成功领取）
                        continue
                    
                    # 情况3：弹窗 - 放弃奖励
                    elif find_and_click("放弃奖励"):
                        print("点击了'放弃奖励'，返回任务页")
                        time.sleep(3)
                        break  # 跳出内层循环，继续外层循环
                    
                    # 其他未知情况
                    else:
                        print("未知状态，尝试等待")
                        # d.press('back')
                        time.sleep(2)
                        break
                else:
                    element = d.xpath("//*[contains(@text,'后可领取')]")
                    if element.exists:
                        text = element.get_text()
                        print(text)
                    if find_and_click("拒绝") is not None:
                        print("拒绝弹出app")
                        d.press('back')
                        time.sleep(2)
                        d.press('back')
                        time.sleep(2)

    ##############################
