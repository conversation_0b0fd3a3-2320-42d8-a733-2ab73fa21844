# 📱 设备管理程序 - PyQt6版本

## 项目简介

这是一个基于 PyQt6 开发的移动设备管理程序，主要用于管理 Android 设备的连接、显示和任务执行。程序提供了直观的图形界面，支持批量操作，是 Web 版本的 1:1 功能复刻，并在性能和用户体验方面进行了优化。

## 系统要求

- Python 3.8+
- PyQt6
- ADB (Android Debug Bridge)
- scrcpy (用于设备屏幕显示)
- Windows 10/11 (推荐，部分功能依赖 Windows API)

## 安装依赖

```bash
pip install PyQt6 psutil
```

## 当前已实现功能

### 🔧 设备管理
- ✅ 添加/编辑/删除设备
- ✅ 设备信息显示和编辑
- ✅ 多URL支持（设备可配置多个连接地址）
- ✅ 设备备注功能
- ✅ 设备配置导入/导出

### 🔗 ADB连接管理
- ✅ 自动检测ADB连接状态
- ✅ 定时刷新连接状态（每5秒）
- ✅ 手动刷新ADB连接
- ✅ 自动重连机制（失败时最多重试3次）
- ✅ 连接状态可视化显示

### 🖥️ 设备显示管理
- ✅ scrcpy集成，支持设备屏幕显示
- ✅ 智能窗口管理（双击打开/置于前台）
- ✅ 实时窗口检测（支持程序启动前的scrcpy窗口）
- ✅ 统一的窗口关闭机制（右键菜单/批量关闭）
- ✅ 窗口并列排列功能
- ✅ 10x设备特殊处理（不同的scrcpy参数）

### ⚙️ 任务执行管理
- ✅ 设备任务运行配置
- ✅ 支持多种执行动作（do_job, test, monitor, open_tmall）
- ✅ 批量运行任务
- ✅ 实时任务状态监控
- ✅ 任务输出日志记录

### 📊 界面和交互
- ✅ 响应式设备卡片布局
- ✅ 设备状态图标显示（空闲/运行/显示）
- ✅ 多选操作支持（Ctrl+点击）
- ✅ 右键菜单功能
- ✅ 分标签页日志系统
- ✅ 双击关闭日志标签页
- ✅ 设备拖拽排序功能（智能排序逻辑）

### 🔍 搜索和过滤
- ✅ 设备信息搜索功能
- ✅ 按名称、IP地址、备注搜索
- ✅ 实时搜索结果显示

### 💾 数据管理
- ✅ JSON格式配置文件
- ✅ 自动保存设备配置
- ✅ 配置文件导入/导出
- ✅ 旧版本数据格式兼容

### 🖱️ 设备排序管理
- ✅ 拖拽排序设备卡片
- ✅ 自定义设备布局保存
- ✅ 设备顺序持久化
- ✅ 智能拖拽检测

## 📋 待完成功能列表

### 🚀 高优先级功能

#### 1. 设备分组功能
- [ ] 设备分组管理
- [ ] 分组折叠/展开
- [ ] 分组拖拽排序
- [ ] 分组颜色标识

#### 2. 高级ADB功能
- [ ] ADB命令执行器
- [ ] 设备信息详细查看（型号、版本、分辨率等）
- [ ] 设备截图功能
- [ ] 文件传输功能（推送/拉取文件）

#### 3. 任务管理增强
- [ ] 任务模板系统
- [ ] 定时任务调度
- [ ] 任务执行历史记录
- [ ] 任务失败重试机制
- [ ] 任务执行统计报告

#### 4. 系统设置完善
- [ ] 完善设置对话框功能
- [ ] 主题切换（亮色/暗色）
- [ ] 字体大小调整
- [ ] 快捷键自定义
- [ ] 启动选项配置

### 🔧 中优先级功能

#### 5. 窗口管理增强
- [ ] 窗口布局预设（1x1, 2x2, 3x3等）
- [ ] 窗口大小同步调整
- [ ] 多显示器支持
- [ ] 窗口置顶功能
- [ ] 全屏模式

#### 6. 日志系统优化
- [ ] 日志级别过滤（错误、警告、信息）
- [ ] 日志导出功能
- [ ] 日志搜索功能
- [ ] 日志自动清理（按时间/大小）
- [ ] 彩色日志显示

#### 7. 设备监控功能
- [ ] 设备性能监控（CPU、内存、电池）
- [ ] 网络状态监控
- [ ] 应用运行状态监控
- [ ] 设备温度监控
- [ ] 监控数据图表显示

#### 8. 批量操作增强
- [ ] 批量设备配置
- [ ] 批量文件操作
- [ ] 操作进度显示
- [ ] 操作结果详细报告
- [ ] 操作撤销功能

### 🎨 低优先级功能

#### 9. 用户体验优化
- [ ] 启动画面
- [ ] 操作引导教程
- [ ] 工具提示增强
- [ ] 动画效果
- [ ] 音效提示

#### 10. 扩展功能
- [ ] 插件系统
- [ ] 脚本执行器
- [ ] API接口
- [ ] 远程控制功能
- [ ] 设备共享功能

#### 11. 数据分析
- [ ] 使用统计
- [ ] 性能分析
- [ ] 错误统计
- [ ] 使用报告生成

#### 12. 系统集成
- [ ] 系统托盘功能
- [ ] 开机自启动
- [ ] Windows服务模式
- [ ] 命令行接口
- [ ] 其他工具集成

## 🐛 已知问题

### 需要修复的Bug
- [ ] 某些情况下设备卡片渲染异常
- [ ] 大量设备时界面响应缓慢
- [ ] scrcpy窗口标题偶尔显示错误
- [ ] 日志标签页过多时性能下降

### 性能优化
- [ ] 设备状态更新频率优化
- [ ] 内存使用优化
- [ ] 启动速度优化
- [ ] 大数据量处理优化

## 📖 使用说明

### 基本操作
1. **添加设备**: 点击菜单栏"设备" -> "添加设备"
2. **运行任务**: 右键设备卡片 -> "运行任务"
3. **打开显示**: 双击设备卡片或右键 -> "打开显示"
4. **批量操作**: 按住Ctrl键选择多个设备，然后右键操作

### 快捷键
- `Ctrl+N`: 添加新设备
- `Ctrl+F`: 搜索设备信息
- `Ctrl+A`: 全选设备
- `Ctrl+D`: 取消选择
- `Ctrl+Q`: 退出程序

## 🤝 贡献指南

欢迎提交Issue和Pull Request来帮助改进这个项目！

### 开发环境设置
1. 克隆项目
2. 安装依赖
3. 运行 `python device_manager_qt.py`

### 代码规范
- 遵循PEP 8代码风格
- 添加适当的注释和文档字符串
- 编写单元测试

## 📚 文档

### 用户文档
- [拖拽功能使用指南](DRAG_DROP_GUIDE.md) - 设备拖拽排序功能详细说明
- [故障排除指南](DRAG_TROUBLESHOOTING.md) - 常见问题解决方案

### 开发文档
- [技术实现细节](TECHNICAL_DETAILS.md) - 核心技术架构和实现
- [架构重构文档](ARCHITECTURE_REFACTOR.md) - 最近重构的详细记录
- [变更日志](CHANGELOG.md) - 版本更新记录

### 测试文档
- [功能测试清单](FEATURE_CHECKLIST.md) - 完整功能验证清单
- [演示脚本](DEMO_SCRIPT.md) - 功能演示和测试脚本

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者

---

**版本**: 1.0.0
**最后更新**: 2024年12月
