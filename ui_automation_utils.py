#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI自动化工具类
提供通用的UI元素查找和点击功能，支持图片匹配、文本匹配和XPath匹配
"""

import time
import cv2
import numpy as np
import os
import re
from device_utils import get_device, get_log_path


class UIAutomationUtils:
    """UI自动化工具类"""
    
    @staticmethod
    def sanitize_filename(name):
        """清理文件名中的非法字符并限制长度"""
        # 替换非法字符为下划线
        clean_name = re.sub(r'[\\/*?:"<>|]', '_', name)
        # 移除首尾空白字符
        clean_name = clean_name.strip()
        # 限制文件名长度（Windows最大255，保留扩展名空间）
        return clean_name[:200] if len(clean_name) > 200 else clean_name
    
    @staticmethod
    def find_and_click(target_name, match_type='文本', pianyi_x=0, pianyi_y=0,
                       threshold=0.7, click=True, wait_after_click=2, screenshot=True):
        """
        通过指定方式定位元素并点击（可选）

        参数:
            target_name (str): 图片名称/文本内容/XPath表达式
            match_type (str): 匹配方式，必须为'图片'/'文本'/'xpath'
            pianyi_x (int): X轴点击偏移量（仅图片匹配时有效）
            pianyi_y (int): Y轴点击偏移量（仅图片匹配时有效）
            threshold (float): 图片匹配阈值(0-1)
            click (bool): 是否执行点击操作
            wait_after_click (int): 点击后等待时间（秒）
            screenshot (bool): 是否保存截图

        返回:
            bool: 图片匹配时返回是否找到
            object: 文本/xpath匹配时返回元素对象（找不到返回None）
        """

        d = get_device()
        log_path = get_log_path()

        if match_type not in ['图片', '文本', 'xpath']:
            raise ValueError("match_type 参数必须为 '图片'/'文本'/'xpath'")

        screenshot_name = f"after_find_and_click_{UIAutomationUtils.sanitize_filename(target_name)}.png"

        element = None

        try:
            # 图片匹配分支
            if match_type == '图片':
                target_img_path = target_name + ".png"
                if not os.path.exists(target_img_path):
                    print(f"[错误] 图片文件不存在: {target_img_path}")
                    return False

                screen_pil = d.screenshot()
                screen_cv = cv2.cvtColor(np.array(screen_pil), cv2.COLOR_RGB2BGR)

                template_img = cv2.imdecode(np.fromfile(
                    target_img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
                h, w = template_img.shape[:2]

                res = cv2.matchTemplate(
                    screen_cv, template_img, cv2.TM_CCOEFF_NORMED)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)

                if max_val >= threshold:
                    center_x = max_loc[0] + w // 2
                    center_y = max_loc[1] + h // 2
                    print(f"[成功] 图片匹配成功 [{target_name}] 置信度: {max_val:.2f}")

                    if click:
                        click_x = center_x + pianyi_x
                        click_y = center_y + pianyi_y
                        d.click(click_x, click_y)
                        print(f"[点击] 已点击坐标: ({click_x}, {click_y})")
                        time.sleep(wait_after_click)

                    return True  # 图片匹配始终返回布尔值
                return False

            # 文本匹配分支
            elif match_type == '文本':
                element = d(text=target_name)

            # XPath匹配分支
            elif match_type == 'xpath':
                element = d.xpath(target_name)

            # 处理元素查找结果
            if element.exists:
                print(f"[成功] {match_type}匹配成功 [{target_name}]")
                if click:
                    element.click()
                    print(f"[点击] 已点击{match_type}元素")
                    time.sleep(wait_after_click)
                return element  # 返回元素对象
            return None  # 找不到返回None

        except Exception as e:
            print(f"[错误] 操作异常: {str(e)}")
            return False if match_type == '图片' else None

        finally:
            if screenshot:
                try:
                    d.screenshot(os.path.join(log_path, screenshot_name))
                    print(f"[截图] 已保存操作后截图: {screenshot_name}")
                except Exception as e:
                    print(f"[错误] 截图保存失败: {str(e)}")

    @staticmethod
    def find_element(target_name, match_type='文本', threshold=0.7):
        """
        查找元素但不点击
        
        参数:
            target_name (str): 图片名称/文本内容/XPath表达式
            match_type (str): 匹配方式，必须为'图片'/'文本'/'xpath'
            threshold (float): 图片匹配阈值(0-1)
        
        返回:
            bool: 图片匹配时返回是否找到
            object: 文本/xpath匹配时返回元素对象（找不到返回None）
        """
        return UIAutomationUtils.find_and_click(
            target_name=target_name,
            match_type=match_type,
            threshold=threshold,
            click=False,
            screenshot=False
        )

    @staticmethod
    def wait_for_element(target_name, match_type='文本', timeout=10, check_interval=1, threshold=0.7):
        """
        等待元素出现
        
        参数:
            target_name (str): 图片名称/文本内容/XPath表达式
            match_type (str): 匹配方式，必须为'图片'/'文本'/'xpath'
            timeout (int): 超时时间（秒）
            check_interval (float): 检查间隔（秒）
            threshold (float): 图片匹配阈值(0-1)
        
        返回:
            bool: 图片匹配时返回是否找到
            object: 文本/xpath匹配时返回元素对象（找不到返回None）
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            result = UIAutomationUtils.find_element(target_name, match_type, threshold)
            
            # 对于图片匹配，True表示找到
            if match_type == '图片' and result:
                return result
            # 对于文本/xpath匹配，非None表示找到
            elif match_type in ['文本', 'xpath'] and result is not None:
                return result
            
            time.sleep(check_interval)
        
        print(f"[超时] 等待元素超时: {target_name} ({match_type})")
        return False if match_type == '图片' else None

    @staticmethod
    def click_if_exists(target_name, match_type='文本', pianyi_x=0, pianyi_y=0,
                       threshold=0.7, wait_after_click=2):
        """
        如果元素存在则点击
        
        参数:
            target_name (str): 图片名称/文本内容/XPath表达式
            match_type (str): 匹配方式，必须为'图片'/'文本'/'xpath'
            pianyi_x (int): X轴点击偏移量（仅图片匹配时有效）
            pianyi_y (int): Y轴点击偏移量（仅图片匹配时有效）
            threshold (float): 图片匹配阈值(0-1)
            wait_after_click (int): 点击后等待时间（秒）
        
        返回:
            bool: 是否成功点击
        """
        result = UIAutomationUtils.find_and_click(
            target_name=target_name,
            match_type=match_type,
            pianyi_x=pianyi_x,
            pianyi_y=pianyi_y,
            threshold=threshold,
            click=True,
            wait_after_click=wait_after_click,
            screenshot=True
        )
        
        # 统一返回布尔值
        if match_type == '图片':
            return result  # 已经是布尔值
        else:
            return result is not None  # 转换为布尔值


# 为了向后兼容，提供全局函数
def find_and_click(target_name, match_type='文本', pianyi_x=0, pianyi_y=0,
                   threshold=0.7, click=True, wait_after_click=2, screenshot=True):
    """
    全局函数版本的 find_and_click，保持向后兼容
    """
    return UIAutomationUtils.find_and_click(
        target_name=target_name,
        match_type=match_type,
        pianyi_x=pianyi_x,
        pianyi_y=pianyi_y,
        threshold=threshold,
        click=click,
        wait_after_click=wait_after_click,
        screenshot=screenshot
    )


# 便捷函数
def find_element(target_name, match_type='文本', threshold=0.7):
    """查找元素但不点击"""
    return UIAutomationUtils.find_element(target_name, match_type, threshold)


def wait_for_element(target_name, match_type='文本', timeout=10, check_interval=1, threshold=0.7):
    """等待元素出现"""
    return UIAutomationUtils.wait_for_element(target_name, match_type, timeout, check_interval, threshold)


def click_if_exists(target_name, match_type='文本', pianyi_x=0, pianyi_y=0,
                   threshold=0.7, wait_after_click=2):
    """如果元素存在则点击"""
    return UIAutomationUtils.click_if_exists(target_name, match_type, pianyi_x, pianyi_y, threshold, wait_after_click)
